import { Injectable, Logger } from '@nestjs/common';
import { spawn } from 'child_process';
import * as path from 'path';

@Injectable()
export class PythonExecutionService {
  private readonly logger = new Logger(PythonExecutionService.name);

  executePythonScript(lng: number, lat: number, range: number) {
    const scriptPath = path.join(__dirname, '..', '..', '..', 'pyScripts', 'script.py');
    const requirementsPath = path.join(__dirname, '..', '..', '..', 'pyScripts', 'requirements.txt');
    this.logger.log(`Executing Python script at: ${scriptPath}`);
    this.logger.log(`Current working directory: ${process.cwd()}`);

    // First, install the required Python libraries
    // const pipInstall = spawn('pip3', ['install', '-r', requirementsPath]);

    // pipInstall.stdout.on('data', (data) => {
    //   this.logger.log(`pip stdout: ${data}`);
    // });

    // pipInstall.stderr.on('data', (data) => {
    //   this.logger.error(`pip stderr: ${data}`);
    // });

    // pipInstall.on('close', (code) => {
    //   if (code !== 0) {
    //     this.logger.error(`pip install exited with code ${code}`);
    //     return;
    //   }

    // After installing libraries, run the Python script
    const pythonProcess = spawn('python3', [scriptPath, '-A', lat.toString(), '-O', lng.toString(), '-l', range.toString()]);

    pythonProcess.stdout.on('data', (data) => {
      this.logger.log(`Python script stdout: ${data}`);
    });

    pythonProcess.stderr.on('data', (data) => {
      this.logger.error(`Python script stderr: ${data}`);
    });

    pythonProcess.on('close', (code) => {
      this.logger.log(`Python script exited with code ${code}`);
    });
    // });
  }
}
