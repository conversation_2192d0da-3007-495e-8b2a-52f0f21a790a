import { Body, Controller, Get, Query } from '@nestjs/common';
import { PythonExecutionService } from './pythonExecution.service';

@Controller('python')
export class PythonExecutionController {
  constructor(
    private readonly pythonExecutionService: PythonExecutionService,
  ) {}

  @Get('liveSimulation')
  executePythonScript(
    @Query('longitude') lng: number,
    @Query('latitude') lat: number,
    @Query('range') range: number,
  ) {
    this.pythonExecutionService.executePythonScript(lng, lat, range);
    return { message: 'Python script execution started' };
  }
}
