import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { EventProfile } from './eventProfile.interface';
import { OrganizationService } from '../organizations/organization.service';
import { EventHistory } from './eventHistory.interface';
import Constants from 'src/common/constants';

@Injectable()
export class EventModel {
  constructor(
    @InjectModel(Constants.event_profile) private readonly eventProfileModel: Model<EventProfile>,
    @InjectModel(Constants.event_history) private readonly eventHistoryModel: Model<EventHistory>,
    @InjectModel('notification')
    private readonly notificationModel: Model<Notification>,
    private readonly organizationService: OrganizationService,
  ) {}

  async findAll(
    skip: number,
    limit: number,
    orgId: string,
    alertZoneId: string,
    search?: string,
    startDate?: Date,
    endDate?: Date,
    sort?: any,
    sortDirection?: string,
  ) {
    console.log("hello hello !")
    const sortObj = {};
    sortObj[sort] = sortDirection;
    const objectIdOrgId = await this.organizationService.findByAuth0Id(orgId);
    console.log('filters: startDate ' + startDate.toISOString() + ' EndDate: ' + endDate.toISOString() + ' search: ' + search, " auth0_id: ", orgId, " acctual org id: ", objectIdOrgId._id.toString());

    const events = await this.notificationModel
      .aggregate([
        {
          $match: {
            org_id: objectIdOrgId._id,
          },
        },

        {
          $match: {
            'timestamp': {
              $gte: startDate,
              $lte: endDate,
            },
            'alertZone._id': alertZoneId,
          },
        },
        {
          $lookup: {
            from: Constants.event_profile,
            localField: 'event_id',
            foreignField: 'EVENT_ID',
            as: 'events',
          },
        },
        {
          $match: {
            $or: !search
              ? [{ _id: { $ne: null } }]
              : [
                  { 'events.OPERATOR_ID': { $regex: search, $options: 'i' } },
                  { 'events.EVENT_ID': { $regex: search, $options: 'i' } },
                  { 'events.UAS_ID': { $regex: search, $options: 'i' } },
                  { 'events.DEVICE_ID': { $regex: search, $options: 'i' } },
                  { 'alertZone.name': { $regex: search, $options: 'i' } },
                  { 'alertZone._id': { $regex: search, $options: 'i' } },
                ],
          },
        },
        { $sort: !sort ? { 'timestamp': -1 } : sortObj },
        {
          $project: {
            _id: 0,
          },
        },

        { $skip: skip },
        { $limit: limit },
      ])
      .exec();

    return events;
  }

  async findEventCountsByAlertZones(orgId: string, search?: string, startDate?: Date, endDate?: Date) {
    console.log('filters: startDate ' + startDate.toISOString() + ' EndDate: ' + endDate.toISOString() + ' orgId: ' + orgId);
    const realOrgId = await this.organizationService.findByAuth0Id(orgId);
    const events = await this.notificationModel
      .aggregate([
        {
          $match: {
            org_id: realOrgId._id,
            'timestamp': {
              $gte: startDate,
              $lte: endDate,
            }
          },
        },
        {
          $lookup: {
            from: Constants.event_profile,
            localField: 'event_id',
            foreignField: 'EVENT_ID',
            as: 'events',
          },
        },
        {
          $match: {
            $or: !search
              ? [{ _id: { $ne: null } }]
              : [
                  { 'events.OPERATOR_ID': { $regex: search, $options: 'i' } },
                  { 'events.EVENT_ID': { $regex: search, $options: 'i' } },
                  { 'events.UAS_ID': { $regex: search, $options: 'i' } },
                  { 'events.DEVICE_ID': { $regex: search, $options: 'i' } },
                  { 'alertZone.name': { $regex: search, $options: 'i' } },
                  { 'alertZone._id': { $regex: search, $options: 'i' } },
                ],
          },
        },
        {
          $group: {
            _id: {
              zoneId: '$alertZone._id',
              zoneName: '$alertZone.name',
            },
            count: {
              $sum: 1,
            },
          },
        },
        {
          $addFields: {
            zoneId: '$_id.zoneId',
            zoneName: '$_id.zoneName',
          },
        },

        {
          $project: {
            _id: 0,
          },
        },
      ])
      .exec();

    return events;
  }

  async findOne(eventId: string, size: number) {
    const eventProfile = await this.eventProfileModel.findOne({EVENT_ID: eventId});

    if (!eventProfile) {
      throw new NotFoundException(`Event with ID ${eventId} not found`);
    }

    const eventHistory = await this.eventHistoryModel.find({ EVENT_ID: eventId}).sort({"TIME_STAMP": -1}).limit(size);
    eventProfile.PROCESSED_ITEMS = eventHistory;


    return eventProfile;
  }
}
