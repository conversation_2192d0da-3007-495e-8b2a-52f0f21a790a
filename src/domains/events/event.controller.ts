// events.controller.ts
import { Controller, Get, NotFoundException, Param, Query, Req } from '@nestjs/common';
import { EventModel } from './event.model'; // Import the EventModel
import { EventProfile } from './eventProfile.interface'; // Assuming you have defined the Event interface

@Controller('api/events')
export class EventsController {
  constructor(private readonly eventModel: EventModel) {} // Inject the EventModel

  @Get('/alertZone/:alertZoneId')
  async findAll(
    @Query('page') page: string = '1',
    @Query('pageSize') pageSize: string = '10',
    @Query('query') query: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Param('alertZoneId') alertZoneId: string,
    @Param('sort') sort: string,
    @Param('sortDirection') sortDirection: string,
    @Req()
    req: Request,
  ): Promise<Event[]> {
    const skip = (parseInt(page) - 1) * parseInt(pageSize);
    const user = req['user'];
    const orgId = user.org_id;
    return await this.eventModel.findAll(
      skip,
      parseInt(pageSize),
      orgId,
      alertZoneId,
      query,
      new Date(startDate),
      new Date(endDate),
      sort,
      sortDirection,
    );
  }

  @Get('/count/alertZone')
  async findEventCountsByAlertZones(
    @Query('query') query: string,
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Req()
    req: Request,
  ): Promise<Event[]> {
    const user = req['user'];
    const orgId = user.org_id;
    return await this.eventModel.findEventCountsByAlertZones(orgId, query, new Date(startDate), new Date(endDate));
  }

  @Get(':id')
  async findOne(@Param('id') id: string, @Query('size') size: string = '0'): Promise<EventProfile> {
    const event = await this.eventModel.findOne(id, parseInt(size));
    if (!event) {
      throw new NotFoundException(`Event with ID ${id} not found`);
    }
    return event;
  }
}
