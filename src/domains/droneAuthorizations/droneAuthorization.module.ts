import { Mo<PERSON><PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { DroneAuthorizationController } from './droneAuthorization.controller';
import { DroneAuthorizationService } from './droneAuthorization.service';
import DroneAuthorizationSchema from './droneAuthorization.schema';
import Constants from 'src/common/constants';
import { AppSyncService } from 'src/utils/appsync.service';
import { OrganizationsModel } from '../organizations/organization.model';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Constants.droneAuthorizations, schema: DroneAuthorizationSchema }])
  ],
  controllers: [DroneAuthorizationController],
  providers: [DroneAuthorizationService, OrganizationsModel, AppSyncService],
  exports: [DroneAuthorizationService, OrganizationsModel, AppSyncService],
})
export class DroneAuthorizationModule {}
