import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { DroneAuthorization } from './droneAuthorization.interface';
import Constants from 'src/common/constants';
import { AppSyncService } from 'src/utils/appsync.service';
import { Organizations } from '../organizations/organization.interface';
import { SystemNotification } from '../systemNotifications/systemNotification.interface';

@Injectable()
export class DroneAuthorizationService {
  constructor(
    @InjectModel(Constants.droneAuthorizations) private droneAuthorizationModel: Model<DroneAuthorization>,
    private appSyncService: AppSyncService,
    @InjectModel('organizations')
    private readonly organizationModel: Model<Organizations>,
  ) {}

  async findAll(skip: number = 0, limit: number = 10): Promise<{ authorizations: DroneAuthorization[], total: number }> {
    const [authorizations, total] = await Promise.all([
      this.droneAuthorizationModel
        .find({ isDeleted: false })
        .skip(skip)
        .limit(limit)
        .sort({ issued_at: -1 })
        .exec(),
      this.droneAuthorizationModel.countDocuments({ isDeleted: false }).exec(),
    ]);

    return { authorizations, total };
  }

  async findByDroneId(droneId: string): Promise<DroneAuthorization[]> {
    return this.droneAuthorizationModel
      .find({ drone_id: new Types.ObjectId(droneId), isDeleted: false })
      .sort({ issued_at: -1 })
      .exec();
  }

  async findOne(id: string): Promise<DroneAuthorization> {
    const authorization = await this.droneAuthorizationModel.findOne({ _id: id, isDeleted: false }).exec();

    if (!authorization) {
      throw new NotFoundException(`Authorization with ID ${id} not found`);
    }

    return authorization;
  }

  async create(authorizationData: Partial<DroneAuthorization>, userId: string): Promise<DroneAuthorization> {
    // Create new authorization
    const newAuthorization = new this.droneAuthorizationModel({
      ...authorizationData,
      createdBy: new Types.ObjectId(userId),
      updatedBy: new Types.ObjectId(userId),
    });

    return newAuthorization.save();
  }

  async publishNotification(notification: SystemNotification) {
    const org = await this.organizationModel.findOne({ _id: notification.org_id });
    this.appSyncService.publishMessage(org.auth0_id, notification);
  }

  async update(id: string, authorizationData: Partial<DroneAuthorization>, userId: string): Promise<DroneAuthorization> {
    // Check if authorization exists
    const authorization = await this.droneAuthorizationModel.findOne({ _id: id, isDeleted: false }).exec();

    if (!authorization) {
      throw new NotFoundException(`Authorization with ID ${id} not found`);
    }

    // Update authorization
    const updatedAuthorization = await this.droneAuthorizationModel.findByIdAndUpdate(
      id,
      {
        ...authorizationData,
        updatedBy: new Types.ObjectId(userId),
        updatedAt: new Date(),
      },
      { new: true }
    ).exec();

    return updatedAuthorization;
  }

  async delete(id: string, userId: string): Promise<{ message: string }> {
    const authorization = await this.droneAuthorizationModel.findOne({ _id: id, isDeleted: false }).exec();

    if (!authorization) {
      throw new NotFoundException(`Authorization with ID ${id} not found`);
    }

    // Soft delete
    await this.droneAuthorizationModel.findByIdAndUpdate(
      id,
      {
        isDeleted: true,
        isActive: false,
        deletedAt: new Date(),
        deletedBy: new Types.ObjectId(userId),
      }
    ).exec();

    return { message: 'Authorization deleted successfully' };
  }

  async getLatestAuthorizationForDrone(droneId: string): Promise<DroneAuthorization | null> {
    return this.droneAuthorizationModel
      .findOne({ drone_id: new Types.ObjectId(droneId), isDeleted: false })
      .sort({ issued_at: -1 })
      .exec();
  }
}
