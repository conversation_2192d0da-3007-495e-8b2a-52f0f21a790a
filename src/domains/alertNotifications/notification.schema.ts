import { Schema } from 'mongoose';
import { NotificationTypeEnum } from 'src/common/enums/NotifocationTypeEnum';

const NotificationSchema = new Schema({
  org_id: Schema.Types.ObjectId,
  alertZone: Array<{ name: String; _id: String }>,
  event_id: String,
  timestamp: Date,
  seen: Boolean,
  type: {
    type: String,
    enum: Object.values(NotificationTypeEnum),
  }, 
});

NotificationSchema.index({ org_id: 1 });
NotificationSchema.index({ event_id: 1 });

export default NotificationSchema;
