import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Notification } from './notification.interface'; // Assuming you have defined the Notification interface
import { ObjectId } from 'mongodb';
import { OrganizationService } from '../organizations/organization.service';
import Constants from 'src/common/constants';

@Injectable()
export class NotificationModel {
  constructor(
    @InjectModel('notification')
    private readonly notificationModel: Model<Notification>,
    private readonly organizationService: OrganizationService
  ) {}

  async findAllForUser(userId: ObjectId, org_id: String) {
    console.log('finding notifications for org: ', org_id, " And user: ", userId);
    const realOrgId = await this.organizationService.findByAuth0Id(org_id);
    console.log('real org Id: ', realOrgId);
    return this.notificationModel
      .aggregate([
        {
          $match: {
            org_id: realOrgId._id,
          }
        },
        {
          $lookup: {
            from: Constants.event_profile,
            localField: 'event_id',
            foreignField: 'EVENT_ID',
            as: 'event',
          },
        },
        {
          $match: {
            'event.COMPLETE': 0
          },
        }
      ])
      .exec();
  }

  findOne(notification_id: string) {
    return this.notificationModel.findById(notification_id);
  }
}
