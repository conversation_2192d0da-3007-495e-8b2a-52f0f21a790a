import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { OrganizationService } from '../organizations/organization.service';
import { User } from '../users/user.schema';
import { H3Service } from 'src/utils/h3.service';

@Injectable()
export class ServiceZoneService {
  constructor(
    private h3Service: H3Service,
    @InjectModel('servicezones') private serviceZonesModel: Model<any>,
    private organizationService: OrganizationService,
    @InjectModel(User.name) private userModel: Model<User>,
  ) { }

  async createServiceZone(serviceZone: any) {
    try {
      const createdServiceZone = await this.serviceZonesModel.create(serviceZone);
      return createdServiceZone;
    } catch (error) {
      if (error.code === 11000) {
        throw new BadRequestException('Service zone already exists');
      }
      console.error('Error creating service zone:', error);
      throw new BadRequestException('Failed to create service zone');
    }
  }

  async createServiceZoneWithGPS(serviceZoneReq: any) {
    try {
      const current_h3_index = this.h3Service.getH3Index(serviceZoneReq.gps.lat, serviceZoneReq.gps.lng, 5);
      const neighbour_h3_indexes = this.h3Service.getNeighbors(current_h3_index);
      const oldServiceZone = await this.findServiceZonesFromH3Indexes([current_h3_index]);
      console.log({ oldServiceZone })
      if (oldServiceZone.length > 0) {
        throw new BadRequestException('Service zone already exists');
      }

      const serviceZone = {
        name: serviceZoneReq.name,
        h3_indexes: [current_h3_index, ...neighbour_h3_indexes],
      };

      const createdServiceZone = await this.serviceZonesModel.create(serviceZone);
      return createdServiceZone;
    } catch (error) {
      if (error.code === 11000) {
        throw new BadRequestException('Service zone already exists');
      }
      console.error('Error creating service zone:', error);
      throw new BadRequestException(error);
    }
  }

  async getServiceZoneById(id: string) {
    try {
      const serviceZone = await this.serviceZonesModel.findById(id);
      return serviceZone;
    } catch (error) {
      console.error('Error finding service zone:', error);
      return [];
    }
  }

  async getAllServiceZones(lat, lng, name: string) {
    try {
      if (lat && lng) {
        const current_h3_index = this.h3Service.getH3Index(lat, lng, 5);
        const serviceZones = await this.findServiceZonesFromH3Indexes([current_h3_index]);
        for (let i = 0; i < serviceZones.length; i++) {
          serviceZones[i].url = this.buildUrl(serviceZones[i].h3_indexes)
        }

        return serviceZones;
      } else if (name) {
        const serviceZones = await this.serviceZonesModel.find({ name });

        for (let i = 0; i < serviceZones.length; i++) {
          serviceZones[i].url = this.buildUrl(serviceZones[i].h3_indexes)
        }
        return serviceZones;
      }
      else {

        const serviceZones = await this.serviceZonesModel.find({}, { _id: 1, name: 1 });
        return serviceZones;
      }
    } catch (error) {
      console.error('Error finding service zones:', error);
      return [];
    }
  }

  private buildUrl(h3_indexes: String[]) {
    let base_URL = 'https://h3geo.org/#hex='
    for (let i = 0; i < h3_indexes.length; i++) {
      base_URL += h3_indexes[i] + '%2C+'
    }
    return base_URL
  }

  async getSubscribedServiceZonesForUser(orgId: String, user: any) {
    try {
      let orgServiceZones = null;
      let userServiceZones = null;
      if (orgId != null) orgServiceZones = await this.organizationsModel.findOne({ auth0_id: orgId });
      if (user != null) userServiceZones = await this.userModel.findOne({ sub: user.sub });
      return {
        orgServiceZones: orgServiceZones ? orgServiceZones['service_zones'] : [],
        userServiceZones: userServiceZones ? userServiceZones['service_zones'] : [],
      };
    } catch (error) {
      console.error('Error finding service zones:', error);

      throw new BadRequestException('Failed to create service zone');
    }
  }

  async deleteServiceZoneById(id: string) {
    try {
      const result = await this.serviceZonesModel.deleteOne({ _id: id });
      return result;
    } catch (error) {
      console.error('Error deleting service zone:', error);
      throw new BadRequestException('Failed to delete service zone');
    }
  }

  async updateServiceZoneById(id: string, serviceZone: any) {
    if (!(await this.getServiceZoneById(id))) {
      throw new BadRequestException('Service zone does not exists');
    }
    try {
      const updatedServiceZone = await this.serviceZonesModel.findByIdAndUpdate(id, serviceZone);
      return updatedServiceZone;
    } catch (error) {
      console.error('Error updating service zone:', error);
      throw new BadRequestException('Failed to update service zone');
    }
  }

  async findServiceZonesFromH3Indexes(h3_indexes: string[]) {
    try {
      const serviceZones = await this.serviceZonesModel.find({
        h3_indexes: { $in: h3_indexes },
      });

      return serviceZones;
    } catch (error) {
      console.error('Error finding service zones:', error);
      throw new BadRequestException('Failed to find service zone');
    }
  }
}
