import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import SystemNotificationSchema from './systemNotification.schema';
import { SystemNotificationService } from './systemNotification.service';
import OrganizationSchema from '../organizations/organization.schema';
import { AppSyncService } from 'src/utils/appsync.service';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: 'system_notification', schema: SystemNotificationSchema }]),
    MongooseModule.forFeature([{ name: 'organizations', schema: OrganizationSchema }]),
  ],
  providers: [SystemNotificationService, AppSyncService],
  exports: [SystemNotificationService, AppSyncService],
})
export class SystemNotificationModule {}
