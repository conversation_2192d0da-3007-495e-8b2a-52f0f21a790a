// src/domains/systemNotifications/systemNotification.service.ts
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types} from 'mongoose';
import { SystemNotification } from './systemNotification.interface';
import { Organizations } from '../organizations/organization.interface';
import { AppSyncService } from 'src/utils/appsync.service';

@Injectable()
export class SystemNotificationService {
  constructor(
    @InjectModel('organizations')
    private readonly organizationModel: Model<Organizations>,
    @InjectModel('system_notification')
    private systemNotificationModel: Model<SystemNotification>,
    private appSyncService: AppSyncService,
  ) {}

  async findAllUnseenForUser(user: any, orgId: string, page: number = 1, limit: number = 10) {
    const realOrgId = await this.organizationModel.findOne({ auth0_id: orgId });
    const skip = (page - 1) * limit;

    const oneMonthAgo = new Date();
    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

    const startDate = new Date(Math.max(
      new Date(user.created_at).getTime(),
      oneMonthAgo.getTime()
    ));

    const [notifications, total] = await Promise.all([
      this.systemNotificationModel
        .find({
          createdAt: { $gte: startDate },
          org_id: realOrgId._id,
          seen_by: { $ne: user._id }
        })
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .exec(),
      this.systemNotificationModel.countDocuments({
        createdAt: { $gte: startDate },
        org_id: realOrgId._id,
        seen_by: { $ne: user._id }
      })
    ]);

    return {
      notifications,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    };
  }

  async markAsSeen(id: string, userId: Types.ObjectId) {
    const systemNotification = await this.systemNotificationModel.updateOne({_id: id }, { $push: { seen_by: userId } });

    if(systemNotification.matchedCount === 0) {
      throw new NotFoundException(`SystemNotification with ID ${id} not found`);
    }

    return systemNotification;
  }

  async markAllAsSeen(userId: Types.ObjectId) {
    await this.systemNotificationModel.updateMany(
      { seen_by: { $ne: userId } },
      { $push: { seen_by: userId } }
    );
    return null;
  }

  async createNotification(notification: SystemNotification, orgId: String) {
    const realOrgId = await this.organizationModel.findOne({ auth0_id: orgId });
    const newNotification = new this.systemNotificationModel(notification);
    newNotification.org_id = new Types.ObjectId(realOrgId._id);
    newNotification.type = notification.type;
    return newNotification.save();
  }

  async publishNotification(notification: SystemNotification) {
    const org = await this.organizationModel.findOne({ _id: notification.org_id });
    this.appSyncService.publishMessage(org.auth0_id, notification);
  }
}