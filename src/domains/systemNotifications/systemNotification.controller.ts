import { Body, Controller, Get, NotFoundException, Param, Patch, Post, Query, Req } from '@nestjs/common';
import { SystemNotification } from './systemNotification.interface';
import { SystemNotificationService } from './systemNotification.service';

@Controller('api/system-notifications')
export class SystemNotificationsController {
  constructor(private readonly systemNotificationService: SystemNotificationService) {} // Inject the SystemNotificationModel

  @Get("me")
  async findAllForUser(
    @Req() req: Request,
    @Query('page') page?: number,
    @Query('pageSize') limit?: number
  ): Promise<{ notifications: SystemNotification[], pagination: any }> {
    const user = req['user'];
    const org_id = req['org_id'];
    return await this.systemNotificationService.findAllUnseenForUser(
      user,
      org_id,
      page ? parseInt(page.toString()) : 1,
      limit ? parseInt(limit.toString()) : 100
    );
  }

  @Patch(':id')
  async markAsSeen(@Req() req: Request, @Param('id') id: string): Promise<SystemNotification> {
    const user = req['user'];
    const systemSystemNotification = await this.systemNotificationService.markAsSeen(id, user._id);
    if (!systemSystemNotification) {
      throw new NotFoundException(`SystemNotification with ID ${id} not found`);
    }
    return null;
  }

  @Patch()
  async markAllAsSeen(@Req() req: Request): Promise<SystemNotification> {
    const user = req['user'];
    return await this.systemNotificationService.markAllAsSeen(user._id);;
  }


  @Post()
  async createNotification(@Req() req: Request, @Body() systemNotification:SystemNotification): Promise<SystemNotification> {;
    systemNotification.createdBy = req['user']._id;
    const newNotification = await this.systemNotificationService.createNotification(systemNotification, req['org_id']);
    await this.systemNotificationService.publishNotification(newNotification);
    return newNotification;
  }
}
