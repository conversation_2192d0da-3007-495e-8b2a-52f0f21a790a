import { <PERSON>, Get, Req, Post, Body, Param, Delete, Put } from '@nestjs/common';
import { UserPreferencesModel } from './userPreferences.model';
import { UserPreferences } from './userPreferences.interface';

@Controller('userPreferences')
export class UserPreferencesController {
  constructor(private readonly userPreferencesService: UserPreferencesModel) {}

  @Get()
  findAll(@Req() req: any): Promise<UserPreferences> {
    return this.userPreferencesService.findOne({ user_id: req.user['_id'] });
  }

  @Get(':id')
  findOne(@Param('id') id: string): Promise<UserPreferences> {
    return this.userPreferencesService.findOne({ _id: id });
  }

  @Post()
  updateOrCreate(@Req() req: Request, @Body() userPreference: any): Promise<UserPreferences> {
    const user = req['user'];
    return this.userPreferencesService.createOrUpdate(user['_id'], userPreference);
  }
}
