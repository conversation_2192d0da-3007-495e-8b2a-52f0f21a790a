import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { UserPreferences } from './userPreferences.interface';

@Injectable()
export class UserPreferencesModel {
  constructor(@InjectModel('userPreferences') private readonly userPreferenceModel: Model<UserPreferences>) {}

  async findAll(filter: any): Promise<UserPreferences[]> {
    return this.userPreferenceModel.find(filter).exec();
  }

  async findOne(find: any): Promise<UserPreferences> {
    return this.userPreferenceModel.findOne(find).exec();
  }

  async createOrUpdate(userId: string, userPreference: any): Promise<UserPreferences> {
    const user = await this.userPreferenceModel
      .findOneAndUpdate(
        { user_id: userId },
        {
          ...userPreference,
          userId: userId,
        },
        { new: true, upsert: true }, // Create a new document if it doesn't exist (upsert)
      )
      .exec();

    return user;
  }
}
