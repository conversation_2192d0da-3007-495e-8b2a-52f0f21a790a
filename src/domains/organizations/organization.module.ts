import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { OrganizationService } from './organization.service';
import { OrganizationsModel } from './organization.model';
import OrganizationSchema from './organization.schema';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: 'organizations', schema: OrganizationSchema }]),
  ],
  providers: [OrganizationService, OrganizationsModel],
  exports: [OrganizationService, OrganizationsModel],
})
export class OrganizationModule {}
