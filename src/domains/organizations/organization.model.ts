import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Organizations } from './organization.interface';

@Injectable()
export class OrganizationsModel {
  constructor(@InjectModel('organizations') private readonly organizationModel: Model<Organizations>) {}

  async findAll(filter: any): Promise<Organizations[]> {
    return this.organizationModel.find(filter).exec();
  }

  async findOne(find: any): Promise<Organizations> {
    return this.organizationModel.findOne(find).exec();
  }

  async createOrUpdate(orgId: string, organization: any): Promise<Organizations> {
    const user = await this.organizationModel
      .findOneAndUpdate(
        { auth0_id: orgId },
        {
          ...organization,
        },
        { new: true, upsert: true },
      )
      .exec();

    return user;
  }
}
