import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Drone } from './drone.interface';
import { OrganizationsModel } from '../organizations/organization.model';
import { DroneAuthorizationService } from '../droneAuthorizations/droneAuthorization.service';
import Constants from 'src/common/constants';

@Injectable()
export class DroneService {
  constructor(
    @InjectModel(Constants.drones) private droneModel: Model<Drone>,
    private organizationsModel: OrganizationsModel,
    private droneAuthorizationService: DroneAuthorizationService,
  ) {}

  async findAll(orgId: string, skip: number = 0, limit: number = 10): Promise<{ drones: Drone[], total: number }> {
    const [drones, total] = await Promise.all([
      this.droneModel
        .find({ orgId, isDeleted: false })
        .skip(skip)
        .limit(limit)
        .sort({ createdAt: -1 })
        .exec(),
      this.droneModel.countDocuments({ orgId, isDeleted: false }).exec(),
    ]);

    return { drones, total };
  }

  async findOne(id: string): Promise<Drone> {
    const drone = await this.droneModel.findOne({ _id: id, isDeleted: false }).exec();

    if (!drone) {
      throw new NotFoundException(`Drone with ID ${id} not found`);
    }

    return drone;
  }

  async findByDeviceIdValidated(deviceId: string): Promise<Drone> {
    const drone = await this.findByDeviceId(deviceId);

    if (!drone) {
      throw new NotFoundException(`Drone with device_id ${deviceId} not found`);
    }

    return drone;
  }

  async findByDeviceId(deviceId: string): Promise<Drone> {
    return this.droneModel.findOne({ device_id: deviceId, isDeleted: false }).exec();
  }

  async create(droneData: Partial<Drone>, userId: string): Promise<Drone> {
    // Validate if device_id already exists
    const existingDrone = await this.droneModel.findOne({
      device_id: droneData.device_id,
      isDeleted: false
    }).exec();

    if (existingDrone) {
      throw new BadRequestException(`Drone with device_id ${droneData.device_id} already exists`);
    }

    // Create new drone
    const newDrone = new this.droneModel({
      ...droneData,
      createdBy: new Types.ObjectId(userId),
      updatedBy: new Types.ObjectId(userId),
    });

    return newDrone.save();
  }

  async createOrUpdateDroneWithAuthorization(
    droneData: Partial<Drone>,
    authorizationData: {
      is_authorized: boolean;
      authorize_expires_at: Date;
      issued_at?: Date;
      authorized_by?: string;
      notes?: string;
    },
    userId: string
  ): Promise<{ drone: Drone, authorization: any }> {
    let drone = await this.findByDeviceId(droneData.device_id);

    // Check if drone is already authorized
    const latestAuthorization = await this.droneAuthorizationService.getLatestAuthorizationForDrone(drone._id.toString());
    if (latestAuthorization) {
      if (latestAuthorization.is_authorized === authorizationData.is_authorized) {
        throw new BadRequestException(`Drone is already ${authorizationData.is_authorized ? 'authorized' : 'unauthorized'}`);
      }
    }

    if (!drone) {
      drone = await this.create(droneData, userId);
    }

    // Then create the authorization
    const authorization = await this.droneAuthorizationService.create({
      drone_id: drone._id,
      is_authorized: authorizationData.is_authorized,
      authorize_expires_at: authorizationData.authorize_expires_at,
      authorized_by: authorizationData.authorized_by || '',
      notes: authorizationData.notes || ''
    }, userId);

    return { drone, authorization };
  }

  async update(id: string, droneData: Partial<Drone>, userId: string): Promise<Drone> {
    // Check if drone exists
    const drone = await this.droneModel.findOne({ _id: id, isDeleted: false }).exec();

    if (!drone) {
      throw new NotFoundException(`Drone with ID ${id} not found`);
    }

    // If device_id is being updated, check if it's unique
    if (droneData.device_id && droneData.device_id !== drone.device_id) {
      const existingDrone = await this.droneModel.findOne({
        device_id: droneData.device_id,
        isDeleted: false,
        _id: { $ne: id }
      }).exec();

      if (existingDrone) {
        throw new BadRequestException(`Drone with device_id ${droneData.device_id} already exists`);
      }
    }

    // Update drone
    const updatedDrone = await this.droneModel.findByIdAndUpdate(
      id,
      {
        ...droneData,
        updatedBy: new Types.ObjectId(userId),
        updatedAt: new Date(),
      },
      { new: true }
    ).exec();

    return updatedDrone;
  }

  async delete(id: string, userId: string): Promise<{ message: string }> {
    const drone = await this.droneModel.findOne({ _id: id, isDeleted: false }).exec();

    if (!drone) {
      throw new NotFoundException(`Drone with ID ${id} not found`);
    }

    // Soft delete
    await this.droneModel.findByIdAndUpdate(
      id,
      {
        isDeleted: true,
        isActive: false,
        deletedAt: new Date(),
        deletedBy: new Types.ObjectId(userId),
      }
    ).exec();

    return { message: 'Drone deleted successfully' };
  }

  async getDroneWithAuthorization(id: string): Promise<any> {
    const drone = await this.findOne(id);
    const authorization = await this.droneAuthorizationService.getLatestAuthorizationForDrone(id);

    return {
      ...drone.toJSON(),
      authorization: authorization ? {
        id: authorization._id,
        authorize_expires_at: authorization.authorize_expires_at,
        is_authorized: authorization.is_authorized,
        authorized_by: authorization.authorized_by,
        notes: authorization.notes
      } : null
    };
  }

  async getDroneWithAuthorizationByDeviceId(deviceId: string): Promise<any> {
    const drone = await this.findByDeviceIdValidated(deviceId);
    // Get the latest authorization for this drone
    const authorization = await this.droneAuthorizationService.getLatestAuthorizationForDrone(drone._id.toString());

    return {
      ...drone.toJSON(),
      authorization: authorization ? {
        id: authorization._id,
        authorize_expires_at: authorization.authorize_expires_at,
        is_authorized: authorization.is_authorized,
        authorized_by: authorization.authorized_by,
        notes: authorization.notes
      } : null
    };
  }
}
