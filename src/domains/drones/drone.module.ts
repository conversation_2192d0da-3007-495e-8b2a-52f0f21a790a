import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { Drone<PERSON>ontroller } from './drone.controller';
import { DroneService } from './drone.service';
import DroneSchema from './drone.schema';
import { OrganizationsModel } from '../organizations/organization.model';
import OrganizationSchema from '../organizations/organization.schema';
import { DroneAuthorizationModule } from '../droneAuthorizations/droneAuthorization.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: 'drones', schema: DroneSchema }]),
    MongooseModule.forFeature([{ name: 'organizations', schema: OrganizationSchema }]),
    DroneAuthorizationModule,
  ],
  controllers: [DroneController],
  providers: [DroneService, OrganizationsModel],
  exports: [DroneService],
})
export class DroneModule {}
