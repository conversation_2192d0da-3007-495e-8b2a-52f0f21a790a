import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { DroneController } from './drone.controller';
import { DroneService } from './drone.service';
import DroneSchema from './drone.schema';
import { DroneAuthorizationModule } from '../droneAuthorizations/droneAuthorization.module';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: 'drones', schema: DroneSchema }]),
    DroneAuthorizationModule,
  ],
  controllers: [DroneController],
  providers: [DroneService],
  exports: [DroneService],
})
export class DroneModule {}
