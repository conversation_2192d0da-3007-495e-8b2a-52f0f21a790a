import { Controller, Get } from '@nestjs/common';
import axios from 'axios';

@Controller('roles')
export class RolesController {
  constructor() {}

  @Get()
  async findAll() {
    const ManagementToken = await this.getManagementToken();

    const roles = await axios.get(`${process.env.AUTH0_MANAGEMENT_API}/api/v2/roles`, {
      headers: {
        Authorization: `Bearer ${ManagementToken.data.access_token}`,
      },
    });

    return roles.data;
  }

  private async getManagementToken() {
    return await axios.post(
      `${process.env.AUTH0_MANAGEMENT_API}/oauth/token`,
      {
        client_id: process.env.AUTH0_MANAGEMENT_API_CLIENT_ID,
        client_secret: process.env.AUTH0_MANAGEMENT_API_CLIENT_SECRET,
        audience: `${process.env.AUTH0_MANAGEMENT_API}/api/v2/`,
        grant_type: 'client_credentials',
      },
      {
        headers: { 'content-type': 'application/json' },
      },
    );
  }
}
