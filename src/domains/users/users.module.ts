import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { User, UserSchema } from './user.schema';
import { AlertZoneModule } from '../alertZones/alertZone.module';
import { OrganizationsModel } from '../organizations/organization.model';
import OrganizationSchema from '../organizations/organization.schema';
import { ServiceZoneService } from '../serviceZone/serviceZone.service';
import ServiceZoneSchema from '../serviceZone/serviceZone.schema';
import { UtilsModule } from 'src/utils/UtilsModule';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: User.name, schema: UserSchema }]),
    AlertZoneModule,
    MongooseModule.forFeature([{ name: 'organizations', schema: OrganizationSchema }]),
    MongooseModule.forFeature([{ name: 'servicezones', schema: ServiceZoneSchema }]),
    UtilsModule,
  ],
  controllers: [UsersController],
  providers: [UsersService, ServiceZoneService, OrganizationsModel],
  exports: [UsersService],
})
export class UsersModule {}
