import { <PERSON>, Get, Req, Post, Body, Param, Delete, Patch, BadRequestException } from '@nestjs/common';
import { UsersService } from './users.service';
import { User } from './user.schema';
import axios from 'axios';
import { OrganizationService } from '../organizations/organization.service';

@Controller('users')
export class UsersController {
  constructor(
    private readonly usersService: UsersService,
    private readonly organizationService: OrganizationService,
  ) { }

  @Get('/me')
  async me(@Req() req: Request): Promise<User> {
    const token = req.headers['authorization'].split(' ')[1];

    const auth0Info = await axios.get(`${process.env.AUTH0_ISSUER_URL}/userinfo`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    try {
      const ManagementToken = await this.getManagementToken();

      const userRolesPromise = axios.get(
        `${process.env.AUTH0_MANAGEMENT_API}/api/v2/organizations/${auth0Info.data.org_id}/members/${auth0Info.data['sub']}/roles`,
        {
          headers: {
            Authorization: `Bearer ${ManagementToken.data.access_token}`,
            maxBodyLength: Infinity,
          },
        },
      );

      const userInfoPromise = axios.get(`${process.env.AUTH0_MANAGEMENT_API}/api/v2/users/${auth0Info.data.sub}`, {
        headers: {
          Authorization: `Bearer ${ManagementToken.data.access_token}`,
        },
      });

      const userOrgsPromise = axios.get(`${process.env.AUTH0_MANAGEMENT_API}/api/v2/users/${auth0Info.data.sub}/organizations`, {
        headers: {
          Authorization: `Bearer ${ManagementToken.data.access_token}`,
        },
      });

      const [userInfo, userOrgs, userRoles] = await Promise.all([userInfoPromise, userOrgsPromise, userRolesPromise]);

      if (!userInfo.data['family_name']) {
        const firstPartOfEmail = (auth0Info.data.email as string).split('@')[0];
        const updatedResultInformationResult = this.updateUserInformationPromise({
          given_name: firstPartOfEmail,
          family_name: firstPartOfEmail,
          username: firstPartOfEmail,
          name: firstPartOfEmail + " " + "‎"
        }, auth0Info.data.sub, ManagementToken.data.access_token).then((result) => {
          return result.data
        }).catch((e) => {
          console.log(e);
        });
      }

      const orgInfo = userOrgs.data.find((org) => org.id === auth0Info.data.org_id);

      const user = auth0Info.data;

      user['phone_number'] = userInfo.data['phone_number'];
      user['phone_verified'] = userInfo.data['phone_verified'];
      user['roles'] = userRoles.data;
      user['last_activity'] = new Date().toISOString();
      user['created_at'] = userInfo.data['created_at']
      user['org_name'] = orgInfo.display_name;

      user['orgs'] = userOrgs.data.map((org) => org.id);

      const org = await this.organizationService.createOrUpdate(orgInfo.id, orgInfo);

      return this.usersService.findOrCreateUser(user);
    } catch (err) {
      console.log(err);
    }
    return this.usersService.findOrCreateUser(auth0Info.data);
  }

  @Post(':userId/assignServiceZone')
  async assignServiceZone(@Param('userId') userId: string, @Body() assignServiceZoneReq: any) {
    return this.usersService.assignServiceZone(userId, assignServiceZoneReq);
  }

  @Post('assignServiceZone')
  async assignServiceZoneToAllUsers(@Body() assignServiceZoneReq: any) {
    return this.usersService.findAll({}).then((users) => {
      users.forEach((user) => this.usersService.assignServiceZone(user['_id'].toString(), assignServiceZoneReq));
    });
  }

  @Get('/organizations/invitees')
  async listOrgInvitations(@Req() req: Request) {
    const userFromToken = req['user'];

    const ManagementToken = await this.getManagementToken();

    const orgInvitations = await axios.get(`${process.env.AUTH0_MANAGEMENT_API}/api/v2/organizations/${userFromToken['org_id']}/invitations`, {
      headers: {
        Authorization: `Bearer ${ManagementToken.data.access_token}`,
      },
    });

    return orgInvitations.data;
  }

  @Get()
  async listUsers(@Req() req: Request) {
    const userFromToken = req['user'];

    const ManagementToken = await this.getManagementToken();

    const dbUsersPromise = this.usersService.findAll({ orgs: { $in: userFromToken['org_id'] } });

    const orgUsersPromise = axios.get(
      `${process.env.AUTH0_MANAGEMENT_API}/api/v2/organizations/${userFromToken['org_id']}/members?fields=user_id,email,picture,name,roles`,
      {
        headers: {
          Authorization: `Bearer ${ManagementToken.data.access_token}`,
        },
      },
    );

    try {
      const [orgUsers, dbUsers] = await Promise.all([orgUsersPromise, dbUsersPromise]);

      orgUsers.data.forEach((orgUser) => {
        const user = dbUsers.find((dbUser) => dbUser['sub'] === orgUser['user_id']);
        const phone_number = user ? user['phone_number'] : '';

        orgUser['phone_number'] = phone_number;
        orgUser['last_activity'] = user ? user['last_activity'] : '';
      });

      return orgUsers.data;
    } catch (e) {
      console.log(e);
    }
  }

  @Post('/invite')
  async inviteUser(@Req() req: Request, @Body() userInviteDto: any) {
    const userFromToken = req['user'];

    const ManagementToken = await this.getManagementToken();

    const existingMembers = [];
    const usersToInvite = [];

    for (const user of userInviteDto['users']) {
      const existingUser = await this.getUserByEmail(ManagementToken.data.access_token, user['email']);
      console.log({ existingUser });
      if (existingUser === null) {
        usersToInvite.push(user);
      } else {
        existingUser['role'] = user['role'];
        existingMembers.push(existingUser);

      }
    }

    console.log({ existingMembers, usersToInvite });
    if (existingMembers.length > 0)
      await this.addUserToOrganization(ManagementToken.data.access_token, userFromToken['org_id'], existingMembers);
    const invitationsPromises = this.inviteUserToOrg(usersToInvite, userFromToken, ManagementToken.data.access_token);
    const invitations = await Promise.all(invitationsPromises);

  }

  async getUserByEmail(managementToken: String, email: String) {
    const users = await axios.get(`${process.env.AUTH0_MANAGEMENT_API}/api/v2/users?q=${email}`, {
      headers: {
        Authorization: `Bearer ${managementToken}`,
      },
    });
    return users.data.length > 0 ? users.data[0] : null;
  }

  inviteUserToOrg(users: Array<String>, userFromToken: any, managementToken: String) {
    return users.map((user) =>
      axios.post(
        `${process.env.AUTH0_MANAGEMENT_API}/api/v2/organizations/${userFromToken['org_id']}/invitations`,
        {
          inviter: {
            name: userFromToken['name'],
          },
          invitee: {
            email: user['email'],
          },
          client_id: process.env.AUTH0_CLIENT_ID,
          roles: [user['role']],
          send_invitation_email: true,
        },
        {
          headers: {
            Authorization: `Bearer ${managementToken}`,
          },
        },
      ).then((result) => {
        return result.data;
      })
    );
  }

  async addUserToOrganization(managementToken: String, org_id: String, members: Array<String>) {
    let data = JSON.stringify({
      members: members.map((member) => member['user_id']),
    });

    let config = {
      method: 'post',
      maxBodyLength: Infinity,
      url: `${process.env.AUTH0_MANAGEMENT_API}/api/v2/organizations/${org_id}/members`,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${managementToken}`,
      },
      data: data,
    };
    await axios.request(config);
    for (const member of members) {
      await this.assignUserRoleToAnOrg(managementToken, [member['role']], org_id, member['user_id']);
    }

  }

  async assignUserRoleToAnOrg(managementToken: String, roles: Array<String>, org_id: String, user_id: String) {
    console.log({ roles, user_id });
    let data = JSON.stringify({
      roles: roles,
    });

    let config = {
      method: 'post',
      maxBodyLength: Infinity,
      url: `${process.env.AUTH0_MANAGEMENT_API}/api/v2/organizations/${org_id}/members/${user_id}/roles`,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${managementToken}`,
      },
      data: data,
    };
    const result = await axios.request(config);
    return result;
  }

  @Post('/:user_id/roles')
  async assignUserRoleToAnOrgEp(@Req() req: Request, @Param('user_id') user_id: string, @Body() roles) {
    const userFromToken = req['user'];

    const ManagementToken = await this.getManagementToken();

    try {
      const result = await this.assignUserRoleToAnOrg(ManagementToken.data.access_token, roles['roles'], userFromToken['org_id'], user_id);
      return result.data;
    } catch (e) {
      console.log(e);
    }
  }

  @Delete('/:user_id/roles')
  async deleteRoleFromOrg(@Req() req: Request, @Param('user_id') user_id: string, @Body() roles) {
    console.log({ roles, user_id });
    const userFromToken = req['user'];
    let data = JSON.stringify({
      roles: roles['roles'],
    });
    const ManagementToken = await this.getManagementToken();

    let config = {
      method: 'delete',
      maxBodyLength: Infinity,
      url: `${process.env.AUTH0_MANAGEMENT_API}/api/v2/organizations/${userFromToken['org_id']}/members/${user_id}/roles`,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${ManagementToken.data.access_token}`,
      },
      data: data,
    };

    const result = await axios.request(config);
    return result.data;
  }

  @Delete()
  async deleteUserFromOrg(@Req() req: Request, @Body() body: any) {
    const userFromToken = req['user'];

    const members = body['members'];

    const ManagementToken = await this.getManagementToken();

    let data = JSON.stringify(body);

    // delete user from org
    let config = {
      method: 'delete',
      maxBodyLength: Infinity,
      url: `${process.env.AUTH0_MANAGEMENT_API}/api/v2/organizations/${userFromToken['org_id']}/members`,
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${ManagementToken.data.access_token}`,
      },
      data: data,
    };

    const result = await axios.request(config);

    // remove user sessions
    for (let i = 0; i < members.length; i++) {
      console.log("hello")
      const member = members[i];
      let deleteUserSessionConfig = {
        method: 'delete',
        maxBodyLength: Infinity,
        url: `${process.env.AUTH0_MANAGEMENT_API}/api/v2/users/${member}/sessions`,
        headers: {
          Authorization: `Bearer ${ManagementToken.data.access_token}`,
        }
      };
      try {

        const resultUserSession = await axios.request(deleteUserSessionConfig);
        console.log(resultUserSession.data);
      } catch (error) {
        console.error('Error deleting user session:', error.response.data);
      }

      // remove user from db
      const user = await this.usersService.findOne({ sub: member });
      const userOrgs = user['orgs'].filter((org) => org !== userFromToken['org_id']);
      if (userOrgs.length === 0) {
        const deleted = await this.usersService.removeUserBySub(member);
        console.log(deleted)
      }
    }

    return result.data;
  }

  @Delete('/organizations/invitees/:invite_id')
  async deleteInvitationToOrg(@Req() req: Request, @Param('invite_id') invite_id: string) {
    const userFromToken = req['user'];

    const ManagementToken = await this.getManagementToken();
    let config = {
      method: 'delete',
      maxBodyLength: Infinity,
      url: `${process.env.AUTH0_MANAGEMENT_API}/api/v2/organizations/${userFromToken['org_id']}/invitations/${invite_id}`,
      headers: {
        Authorization: `Bearer ${ManagementToken.data.access_token}`,
      },
    };

    const result = await axios.request(config);
    return result.data;
  }

  @Patch()
  async updateMyInfo(@Req() req: Request, @Body() userDto: any) {
    const userFromToken = req['user'];

    console.log({ userFromToken, userDto });

    const ManagementToken = await this.getManagementToken();

    const isPhoneNumberValid = await this.checkTwillioNumber(userDto.phone_number);

    if(!isPhoneNumberValid.data.valid){
      throw new BadRequestException("Phone number is not valid");
    }

    let data = JSON.stringify({
      phone_number: isPhoneNumberValid.data.phone_number,
      given_name: userDto.given_name,
      family_name: userDto.family_name,
      name: userDto.name,
      nickname: userDto.nickname,
      // picture: userDto.picture,
      client_id: process.env.AUTH0_CLIENT_ID,
    });

    let config = {
      method: 'patch',
      maxBodyLength: Infinity,
      url: `${process.env.AUTH0_MANAGEMENT_API}/api/v2/users/${userFromToken['sub']}`,
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        Authorization: `Basic ${ManagementToken.data.access_token}`,
      },
      data: data,
    };
    try {
      const result = await axios.request(config);
    } catch (e) {
      console.log(e);
      throw e.response.data;
    }
  }

  private async checkTwillioNumber(phone_number: String) {
    return await axios.get(
      `https://lookups.twilio.com/v2/PhoneNumbers/${phone_number}`,
      {
        headers: { 'content-type': 'application/json',
          "Authorization": "Basic ********************************************************************************************"
         },
      },
    );

  }

  @Patch('/:userSub')
  async updateUserInfo(@Param('userSub') userSub: string, @Body() userDto: any) {
    const ManagementToken = await this.getManagementToken();

    try {
      const result = await this.updateUserInformationPromise(userDto, userSub, ManagementToken.data.access_token);
    } catch (e) {
      console.log(e);
      throw e.response.data;
    }


  }

  async updateUserInformationPromise(userDto, userSub: string, access_token: string) {
    let data = JSON.stringify({
      phone_number: userDto.phone_number,
      given_name: userDto.given_name,
      family_name: userDto.family_name,
      name: userDto.name,
      nickname: userDto.nickname,
      // picture: userDto.picture,
      client_id: process.env.AUTH0_CLIENT_ID,
    });

    let config = {
      method: 'patch',
      maxBodyLength: Infinity,
      url: `${process.env.AUTH0_MANAGEMENT_API}/api/v2/users/${userSub}`,
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
        Authorization: `Bearer ${access_token}`,
      },
      data: data,
    };
    return axios.request(config);
  }

  private async getManagementToken() {
    return await axios.post(
      `${process.env.AUTH0_MANAGEMENT_API}/oauth/token`,
      {
        client_id: process.env.AUTH0_MANAGEMENT_API_CLIENT_ID,
        client_secret: process.env.AUTH0_MANAGEMENT_API_CLIENT_SECRET,
        audience: `${process.env.AUTH0_MANAGEMENT_API}/api/v2/`,
        grant_type: 'client_credentials',
      },
      {
        headers: { 'content-type': 'application/json' },
      },
    );
  }
}
