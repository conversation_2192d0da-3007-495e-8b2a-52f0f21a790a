import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import AlertZoneSchema from './alertZone.schema';
import { AlertZoneService } from './alertZone.service';
import { ServiceZoneModule } from '../serviceZone/serviceZone.module';
import OrganizationSchema from '../organizations/organization.schema';
import { OrganizationsModel } from '../organizations/organization.model';
import { H3Service } from 'src/utils/h3.service';

@Module({
  imports: [
    MongooseModule.forFeature([{ name: 'alertZone', schema: AlertZoneSchema }]),
    ServiceZoneModule,
    MongooseModule.forFeature([{ name: 'organizations', schema: OrganizationSchema }]),
  ],
  providers: [AlertZoneService, OrganizationsModel, H3Service],
  exports: [AlertZoneService],
})
export class AlertZoneModule {}
