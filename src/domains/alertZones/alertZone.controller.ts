import { Controller, Post, Body, Get, Query, Req, Delete, Param, Patch } from '@nestjs/common';
import { AlertZoneService } from './alertZone.service';
import { UsersService } from '../users/users.service';

@Controller('api/alertZones')
export class AlertZoneController {
  constructor(
    private readonly alertZoneService: AlertZoneService,
    private readonly usersService: UsersService,
  ) {}

  @Post()
  async createAlertZone(@Req() req: Request, @Body() alertZoneDto: any) {
    const user = req['user'];
    return this.alertZoneService.createAlertZone(alertZoneDto, user);
  }

  @Post('organization')
  async createAlertZoneForOrg(@Req() req: Request, @Body() alertZoneDto: any) {
    const user = req['user'];
    return this.alertZoneService.createOrganizationAlertZone(alertZoneDto, user);
  }

  @Get()
  async findalertZonesContainingPoint(@Query('longitude') longitude: number, @Query('latitude') latitude: number) {
    return this.alertZoneService.findAlertZonesContainingPoint({
      longitude,
      latitude,
    });
  }

  @Get('userswithalertzones')
  async findUsersWithAlertZonesContainingPoint(@Query('longitude') longitude: number, @Query('latitude') latitude: number) {
    return this.alertZoneService.findUsersWithAlertZonesContainingPoint({
      longitude,
      latitude,
    });
  }

  @Patch(':id/status')
  async updateAlertZoneStatus(
    @Param('id') id: string,
    @Body('isActive') isActive: boolean,
    @Req() req: Request
  ) {
    const user = req['user'];
    return this.alertZoneService.updateAlertZoneStatus(id, isActive, user['_id']);
  }

  @Delete(':id')
  async deleteServiceZoneById(@Param('id') id: string, @Req() req: Request) {
    const user = req['user'];
    return this.alertZoneService.deleteAlertZoneById(id,user['_id']);
  }
}
