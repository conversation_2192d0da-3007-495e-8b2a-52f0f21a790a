import { Modu<PERSON>, MiddlewareConsumer } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { DatabaseModule } from './database/database.module';
import { UsersModule } from './domains/users/users.module';
import { ConfigModule } from '@nestjs/config';
import { AuthMiddleware } from './common/middleware/auth.middleware';
import { EventsController } from './domains/events/event.controller';
import { AlertZoneController } from './domains/alertZones/alertZone.controller';
import { AlertZoneModule } from './domains/alertZones/alertZone.module';
import { NotificationsController } from './domains/alertNotifications/notification.controller';
import { ServiceZoneModule } from './domains/serviceZone/serviceZone.module';
import { PythonExecutionController } from './domains/pythonExecution/pythonExecution.controller';
import { PythonExecutionModule } from './domains/pythonExecution/pythonExecution.module';
import { UserPreferencesController } from './domains/userPreferences/userPreferences.controller';
import { ServiceZonesController } from './domains/serviceZone/serviceZone.controller';
import { RolesController } from './domains/roles/roles.controller';
import { H3Service } from './utils/h3.service';
import { SystemNotificationsController } from './domains/systemNotifications/systemNotification.controller';
import { SystemNotificationModule } from './domains/systemNotifications/systemNotification.module';
import { DroneModule } from './domains/drones/drone.module';
import { DroneController } from './domains/drones/drone.controller';
import { DroneAuthorizationModule } from './domains/droneAuthorizations/droneAuthorization.module';
import { DroneAuthorizationController } from './domains/droneAuthorizations/droneAuthorization.controller';
// import { RedisModule } from './redis/redis.module';
@Module({
  imports: [
    SystemNotificationModule,
    UsersModule,
    AlertZoneModule,
    DatabaseModule,
    ServiceZoneModule,
    PythonExecutionModule,
    DroneModule,
    DroneAuthorizationModule,
    ConfigModule.forRoot()
  ],
  controllers: [
    AppController,
    EventsController,
    AlertZoneController,
    NotificationsController,
    PythonExecutionController,
    UserPreferencesController,
    ServiceZonesController,
    RolesController,
    SystemNotificationsController,
    DroneController,
    DroneAuthorizationController
  ],
  providers: [AppService, H3Service],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(AuthMiddleware).forRoutes('*');
  }
}
