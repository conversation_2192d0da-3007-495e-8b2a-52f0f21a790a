# Base image
FROM node:20.9.0-alpine3.18

# Create app directory
WORKDIR /usr/src/app

# A wildcard is used to ensure both package.json AND yarn.lock are copied
COPY package*.json ./

RUN apk add --no-cache git

# Install Python 3 and pip
RUN apk --update --no-cache add python3~3.11 --repository=http://dl-cdn.alpinelinux.org/alpine/edge/main  py3-pip

RUN python --version

# Install app dependencies
RUN npm install --frozen-lockfile 

# Remove Yarn cache after installation
# RUN yarn cache clean --mirror

# Bundle app source
COPY . .

RUN pip install -r ./pyScripts/requirements.txt

# Argument to determine environment (default is "staging")
ARG ENVIRONMENT=staging

# Create and populate .env file based on the environment argument

RUN if [ "$ENVIRONMENT" = "dev" ]; then cp .env.dev .env; else cp .env.staging .env; fi

RUN if [ "$ENVIRONMENT" = "qa" ]; then cp .env.qa .env; fi

RUN if [ "$ENVIRONMENT" = "prod" ]; then cp .env.prod .env; fi


# Creates a "dist" folder with the production build
RUN yarn build

# Expose the port on which the app will run
EXPOSE $PORT

# Start the server using the production build
CMD ["npm", "run", "start:prod"]
