---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: coddn-api
  namespace: prod
  labels:
    app: coddn-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: coddn-api
  template:
    metadata:
      labels:
        app: coddn-api
    spec:
      containers:
        - name: coddn-api
          image: 794038255033.dkr.ecr.us-east-2.amazonaws.com/prod/coddn-api:latest
          ports:
            - containerPort: 3001
          env:
            - name: PORT
              value: '3001'
