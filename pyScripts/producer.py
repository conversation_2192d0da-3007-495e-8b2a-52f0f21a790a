from kafka import KafkaProducer
from kafka.errors import KafkaError
import socket
import time
from aws_msk_iam_sasl_signer import MSKAuthTokenProvider
from botocore.credentials import CredentialProvider, Credentials
import os

ACCESS_KEY = os.getenv("AWS_ACCESS_KEY_ID")
SECRET_KEY = os.getenv("AWS_SERCRET_KEY_ID")
REGION = os.getenv("AWS_REGION")
BOOTSTRAP_SERVERS = os.getenv("MSK_PYTHON_BROKERS")

print(ACCESS_KEY, SECRET_KEY, REGION, BOOTSTRAP_SERVERS)
class TestCredentialProvider(CredentialProvider):
    __test__ = False

    def load(self):
        return Credentials(
            access_key=ACCESS_KEY,
            secret_key=SECRET_KEY,
        )

class MSKTokenProvider():
    def token(self):
        token, _ = MSKAuthTokenProvider.generate_auth_token_from_credentials_provider(REGION,TestCredentialProvider())
        print(token)
        return token



class Producer():
    tp = MSKTokenProvider()
    producer = KafkaProducer(
        bootstrap_servers= BOOTSTRAP_SERVERS,
        security_protocol='SASL_SSL',
        sasl_mechanism='OAUTHBEARER',
        sasl_oauth_token_provider=tp,
        client_id=socket.gethostname(),
        api_version=(0,11)
    )
    def produce_message(self, topic, message, partition_key):
        try:
            key_bytes = partition_key.encode() if partition_key else None
            print("Producing message to topic", topic, "key ", partition_key)
            self.producer.send(topic, key=key_bytes, value=message.encode())
            self.producer.flush()
            print("Produced!", topic)
        except Exception as e:
            print("Failed to send message:", e)