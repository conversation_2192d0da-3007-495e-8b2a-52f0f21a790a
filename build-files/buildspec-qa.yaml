version: 0.2

phases:
  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - aws --version
      - aws ecr get-login-password --region us-east-2 | docker login --username AWS --password-stdin 399444019738.dkr.ecr.us-east-2.amazonaws.com
      - echo Configuring AWS CLI
      - aws configure set aws_access_key_id ********************
      - aws configure set aws_secret_access_key D5Ggouj0zo7CuTZHEfIeyP+kiBd5kh5bamzGW5Kh
      - aws configure set region us-east-2
      - aws eks update-kubeconfig --region us-east-2 --name coddn
      - docker login --username alexmedina443 --password ************************************
  build:
    commands:
      - echo Build started on `date`
      - echo Building the Docker image...
      - docker build --build-arg ENVIRONMENT=qa -t coddn-api .
  post_build:
    commands:
      - echo Build completed on `date`
      - echo Pushing the Docker image to ECR...
      - IMAGE_TAG=$(date +%Y%m%d%H%M%S)
      - docker tag coddn-api:latest 399444019738.dkr.ecr.us-east-2.amazonaws.com/qa/coddn-api:$IMAGE_TAG
      - docker push 399444019738.dkr.ecr.us-east-2.amazonaws.com/qa/coddn-api:$IMAGE_TAG
      - kubectl apply -f deployment-files/deployment-qa.yaml
      - kubectl set image deployment/coddn-api-qa coddn-api-qa=399444019738.dkr.ecr.us-east-2.amazonaws.com/qa/coddn-api:$IMAGE_TAG -n coddn-qa
artifacts:
  files:
    - '**/*'
  discard-paths: yes