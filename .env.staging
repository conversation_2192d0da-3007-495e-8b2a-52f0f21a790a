MSK_BROKERS='["b-2-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198","b-1-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198","b-3-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198"]'

MONGODB_CONNECTION_STRING="mongodb+srv://alexmedina:<EMAIL>/?retryWrites=true&w=majority&appName=CoddnStaging"
DATABASE_NAME=coddn

AWS_ACCESS_KEY_ID=********************
AWS_SERCRET_KEY_ID=4XFYdQrDYfxDScDczKgTvs/IETy/Amkf/My4pXjM
AWS_REGION=us-east-2
MSK_PYTHON_BROKERS='b-2-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-1-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198,b-3-public.coddn.fprhcu.c2.kafka.us-east-2.amazonaws.com:9198'

AUTH0_BASE_URL=http://localhost:3000
# AUTH0_ISSUER_URL=https://dev-mquz0q4oastb8zws.us.auth0.com
AUTH0_ISSUER_URL=https://auth-demo.aerodefense.tech
AUTH0_AUDIENCE=https://demo-api.aerodefense.tech
AUTH0_CLIENT_ID=P8bDXvNuR432Wayf0ceKnXYVOXX82KHh
AUTH0_SECRET_KEY=****************************************************************

AUTH0_MANAGEMENT_API=https://dev-mquz0q4oastb8zws.us.auth0.com
AUTH0_MANAGEMENT_API_CLIENT_ID=QMGaKc2LSBZgvVB9Pdztbwj8B5X4sGL9
AUTH0_MANAGEMENT_API_CLIENT_SECRET=****************************************************************

REDIS_HOST="coddn-api-redis-9nthzl.serverless.use2.cache.amazonaws.com"
GROUP_ID=stagingGroupApi

RID_UI_TOPIC="DETECTION"
RID_TOPIC="RID"

APP_SYNC_URL='https://teotickbcvcu5lkpi3lcm626w4.appsync-api.us-east-2.amazonaws.com/graphql'
APP_SYNC_API_KEY='da2-nd5l4qk3b5ffvkwexrodozmlsi'

REDIS_HOST=
