{"name": "CoDDN_API", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^10.3.10", "@nestjs/config": "^3.2.2", "@nestjs/core": "^10.3.10", "@nestjs/microservices": "^10.3.10", "@nestjs/mongoose": "^10.0.6", "@nestjs/platform-express": "^10.3.10", "@nestjs/platform-socket.io": "^10.3.10", "@nestjs/websockets": "^10.3.10", "@socket.io/redis-adapter": "^8.3.0", "@valkey/valkey-glide": "^1.3.2", "aws-msk-iam-sasl-signer-js": "https://github.com/aws/aws-msk-iam-sasl-signer-js", "axios": "^1.7.2", "express-oauth2-jwt-bearer": "^1.6.0", "h3-js": "^4.1.0", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "jwks-rsa": "^3.1.0", "mongoose": "^8.6.0", "node-fetch": "^3.3.2", "redis": "^4.6.15", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "socket.io": "^4.7.5"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}